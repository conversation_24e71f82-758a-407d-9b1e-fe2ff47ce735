declare namespace Question {
  /**
   * 登录模块
   *
   * - single-choice: 单选题
   * - MultipleChoice: 多选题
   * - TrueFalse: 判断题
   * - FillBlank: 填空题
   */
    type QuestionModule = 'SingleChoice' | 'MultipleChoice' | 'TrueFalse' | 'FillBlank'
    // edit 编辑 answer 回答 preview 预览
    type QuestionType = 'edit' | 'answer' | 'preview'
    // 定义流式数据的接口
    interface QuestionData {
      Question: {
        QuestionType: string
        QuestionTypeId: string
        Title: string
        Options: QuestionOption[] | null // 只有选择，判断，多选题有选项
        Answer: string
        Analysis: string
        Chapters: QuestionChapter[] | null // 只有章节出题时才会有数据
        KnowledgePoints: QuestionKnowledgePoints[] | null // 只有知识点出题时才会有数据
      }
    }
    interface QuestionOption {
    /**
     * 选项内容
     */
      Content: string
      /**
       * 选项标识（A、B、C、D）
       */
      Option: string
    }
    interface QuestionChapter {
    /**
     * 章节名称
     */
      ChapterName: string
      /**
       * 章节ID
       */
      Id: string
    }
    interface QuestionKnowledgePoints {
    /**
     * 知识点内容
     */
      Content: string
      /**
       * 知识点ID
       */
      Id: string
      /**
       * 知识点层级
       */
      Level: number
    }
    /**
     * 处理后的题目数据结构
     * 用于前端组件展示和交互的标准格式
     */
    interface TransformToVoQuestionData {
      /** 题目唯一标识符 */
      id: string
      /** 题型文本描述（如"单选题"、"多选题"等） */
      typeText: string
      /** 题型ID（对应后端定义的题型标识） */
      typeId: string
      /** 题目内容/题干 */
      title: string
      /** 对应前端组件名称（用于动态加载组件） */
      componentsName: string
      /**
       * 题目选项（适用于选择题）
       * @property label - 选项显示文本
       * @property value - 选项实际值
       */
      options: Array<{
        label: string
        value: string
      }> | null
      /** 正确答案（格式根据题型不同而变化） */
      correctAnswer: string
      /** 答案解析 */
      analysis: string
      /** 关联的知识点列表 */
      knowledgePoints: QuestionKnowledgePoints[] | null
      /** 章节列表 */
      chapters: QuestionChapter[] | null
      /** 用户答案（单选为string，多选为string[]） */
      userAnswer: string | string[] | null
    }
}
