<script setup lang="ts">
import QSingleChoice from './q-single-choice'
import QMultipleChoice from './q-multiple-choice'
import QTrueFalse from './q-true-false'
import QuestionStem from './question-stem'

defineOptions({
  name: 'QuestionItemContainer',
})
const props = withDefaults(defineProps<Props>(), {
  type: 'preview',
  showQuestionStem: true,
  showNumber: false,
  showType: false,
})

const emit = defineEmits<{
  imageClick: [imageSrc: string]
}>()

interface Props {
  /** 题目类型：编辑/答题/预览模式 */
  type?: Question.QuestionType
  /** 是否显示题干部分 */
  showQuestionStem?: boolean
  /** 是否显示题目序号 */
  showNumber?: boolean
  /** 是否显示题目类型标签 */
  showType?: boolean
}
interface QuestionModule {
  label: string
  component: Component
}
const itemInfo = defineModel<Question.TransformToVoQuestionData>('itemInfo', {
  default: () => ({}),
})
const moduleMap: Partial<Record<Question.QuestionModule, QuestionModule>> = {
  SingleChoice: { label: '单选题', component: QSingleChoice },
  MultipleChoice: { label: '多选题', component: QMultipleChoice },
  TrueFalse: { label: '判断题', component: QTrueFalse },
  // FillBlank: { label: '填空题', component: QFillBlank },
}

const activeModule = computed(() => {
  const componentName = itemInfo.value.componentsName || 'SingleChoice'
  return moduleMap[componentName as Question.QuestionModule] || moduleMap.SingleChoice
})

// 判断是否需要双向绑定
const needsVModel = computed(() => {
  return props.type === 'edit' || props.type === 'answer'
})

// 处理图片点击事件
function handleImageClick(imageSrc: string) {
  emit('imageClick', imageSrc)
}
</script>

<template>
  <div>
    <!-- 题干部分 - 统一在容器中处理 -->
    <QuestionStem
      v-if="props.showQuestionStem"
      :item="itemInfo"
      :show-number="props.showNumber"
      :show-type="props.showType"
      @image-click="handleImageClick"
    />

    <!-- 需要双向绑定的模式 (edit/answer) -->
    <component
      :is="activeModule.component"
      v-if="activeModule && needsVModel"
      v-model:item="itemInfo"
      :type="props.type"
      :show-question-stem="false"
    />
    <!-- 只显示的模式 (preview) -->
    <component
      :is="activeModule.component"
      v-else-if="activeModule && !needsVModel"
      :item="itemInfo"
      :type="props.type"
      :show-question-stem="false"
    />
    <!-- 如果组件不存在，显示提示信息 -->
    <div v-if="!activeModule" class="py-4 text-center text-gray-500">
      <p>题型组件 "{{ itemInfo.componentsName }}" 暂未实现</p>
      <p class="mt-2 text-sm">
        当前使用单选题组件作为默认显示
      </p>
    </div>
  </div>
</template>
