/**
 * 题型数据处理工具
 * 用于处理流式数据并转换为组件需要的格式
 */

// 导入拆分后的模块
import { QuestionReceiveTransformer } from './question-receive-transformer'
import { QuestionSubmitTransformer } from './question-submit-transformer'
import type { GeneratedQuestion } from './question-types'

// 重新导出类型定义
export * from './question-types'

/**
 * 题目数据转换器类（统一入口）
 * 负责处理题目数据的接收转换和提交转换
 */
export class QuestionDataConverter {
  /**
   * 接收转换：将API数据转换为前端组件数据
   * @param data 原始API数据
   * @returns 转换后的前端组件数据
   */
  public static receiveTransform(data: Question.QuestionData): Question.TransformToVoQuestionData {
    return QuestionReceiveTransformer.transform(data)
  }

  /**
   * 提交转换：将前端组件数据转换为API所需格式
   * @param question 前端组件数据
   * @returns 转换后的API数据
   */
  public static submitTransform(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    return QuestionSubmitTransformer.transform(question)
  }

  /**
   * 批量接收转换
   * @param dataList API数据数组
   * @returns 转换后的前端组件数据数组
   */
  public static batchReceiveTransform(dataList: Question.QuestionData[]): Question.TransformToVoQuestionData[] {
    return QuestionReceiveTransformer.batchTransform(dataList)
  }

  /**
   * 批量提交转换
   * @param questions 前端题目数据数组
   * @returns 转换后的API格式数据数组
   */
  public static batchSubmitTransform(questions: Question.TransformToVoQuestionData[]): GeneratedQuestion[] {
    return QuestionSubmitTransformer.batchTransform(questions)
  }

  /**
   * 安全接收转换：带验证的转换方法
   * @param data API数据
   * @returns 转换后的前端组件数据，如果数据无效则返回null
   */
  public static safeReceiveTransform(data: any): Question.TransformToVoQuestionData | null {
    return QuestionReceiveTransformer.safeTransform(data)
  }

  /**
   * 安全提交转换：带验证的转换方法
   * @param question 前端组件数据
   * @returns 转换后的API格式数据，如果数据无效则返回null
   */
  public static safeSubmitTransform(question: any): GeneratedQuestion | null {
    return QuestionSubmitTransformer.safeTransform(question)
  }

  /**
   * 获取题型对应的组件名称
   * @param typeId 题型ID
   * @returns 对应的组件名称
   */
  public static getQuestionComponentName(typeId: string): string {
    return QUESTION_TYPE_COMPONENT_MAP[typeId] || QuestionComponentName.SINGLE_CHOICE
  }
}

// ============= 向后兼容的导出函数 =============

/**
 * 主要的数据处理函数（向后兼容）
 * 根据题型ID处理不同类型的题目数据
 * @deprecated 请使用 QuestionDataConverter.receiveTransform 替代
 */
export function processQuestionData(data: Question.QuestionData): Question.TransformToVoQuestionData {
  return QuestionDataConverter.receiveTransform(data)
}

/**
 * 将前端题目数据转换为API所需的GeneratedQuestion格式（向后兼容）
 * @deprecated 请使用 QuestionDataConverter.submitTransform 替代
 */
export function convertToGeneratedQuestion(question: Question.TransformToVoQuestionData): GeneratedQuestion {
  return QuestionDataConverter.submitTransform(question)
}

/**
 * 批量转换题目数据（向后兼容）
 * @deprecated 请使用 QuestionDataConverter.batchSubmitTransform 替代
 */
export function convertQuestionsToGeneratedQuestions(questions: Question.TransformToVoQuestionData[]): GeneratedQuestion[] {
  return QuestionDataConverter.batchSubmitTransform(questions)
}
