/**
 * 题目数据转换相关的类型定义
 */

/**
 * 通用的生成题目接口
 * 兼容QuestionsApi.GeneratedQuestion格式
 */
export interface GeneratedQuestion {
  /** 答案解析 */
  Analysis: string
  /** 正确答案 */
  Answer: string
  /** 关联的章节列表 */
  Chapters: QuestionChapter[] | null
  /** 关联的知识点列表 */
  KnowledgePoints: QuestionKnowledgePoints[] | null
  /** 选项 */
  Options: QuestionOption[] | null
  /** 题型 */
  QuestionType: string
  /** 题型ID */
  QuestionTypeId: string
  /** 题干 */
  Title: string
}

/**
 * 题目选项接口
 */
export interface QuestionOption {
  /** 选项内容 */
  Content: string
  /** 选项标识（A、B、C、D） */
  Option: string
}

/**
 * 题目关联的知识点信息
 */
export interface QuestionKnowledgePoints {
  /** 知识点内容 */
  Content: string
  /** 知识点ID */
  Id: string
  /** 知识点层级 */
  Level: number
}

/**
 * 题目关联的章节信息
 */
export interface QuestionChapter {
  /** 章节名称 */
  ChapterName: string
  /** 章节ID */
  Id: string
}

/**
 * 题型ID枚举
 */
export enum QuestionTypeId {
  /** 单选题 */
  SINGLE_CHOICE = '2',
  /** 判断题 */
  TRUE_FALSE = '11',
  /** 多选题 */
  MULTIPLE_CHOICE = '10',
  /** 填空题 */
  FILL_BLANK = '4',
}

/**
 * 组件名称枚举
 */
export enum QuestionComponentName {
  /** 单选题组件 */
  SINGLE_CHOICE = 'SingleChoice',
  /** 判断题组件 */
  TRUE_FALSE = 'TrueFalse',
  /** 多选题组件 */
  MULTIPLE_CHOICE = 'MultipleChoice',
  /** 填空题组件 */
  FILL_BLANK = 'FillBlank',
}

/**
 * 题型ID到组件名称的映射
 */
export const QUESTION_TYPE_COMPONENT_MAP: Record<string, string> = {
  [QuestionTypeId.SINGLE_CHOICE]: QuestionComponentName.SINGLE_CHOICE,
  [QuestionTypeId.TRUE_FALSE]: QuestionComponentName.TRUE_FALSE,
  [QuestionTypeId.MULTIPLE_CHOICE]: QuestionComponentName.MULTIPLE_CHOICE,
  [QuestionTypeId.FILL_BLANK]: QuestionComponentName.FILL_BLANK,
}

/**
 * 获取题型对应的组件名称
 * @param typeId 题型ID
 * @returns 对应的组件名称
 */
export function getQuestionComponentName(typeId: string): string {
  return QUESTION_TYPE_COMPONENT_MAP[typeId] || QuestionComponentName.SINGLE_CHOICE
}
